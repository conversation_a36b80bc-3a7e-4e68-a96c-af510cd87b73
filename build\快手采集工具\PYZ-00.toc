('C:\\Users\\<USER>\\Desktop\\快手采集\\build\\快手采集工具\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_pyrepl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bisect.py',
   'PYMODULE'),
  ('brotli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\brotli.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\calendar.py',
   'PYMODULE'),
  ('category_parser',
   'C:\\Users\\<USER>\\Desktop\\快手采集\\category_parser.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('contourpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('cycler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('data_collector',
   'C:\\Users\\<USER>\\Desktop\\快手采集\\data_collector.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('kiwisolver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\lzma.py',
   'PYMODULE'),
  ('matplotlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\backend_qt.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qtagg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\backend_qtagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\qt_compat.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor._formlayout',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\_formlayout.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor.figureoptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\figureoptions.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pylab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickle.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pprint.py',
   'PYMODULE'),
  ('product_query',
   'C:\\Users\\<USER>\\Desktop\\快手采集\\product_query.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\random.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\signal.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipimport.py',
   'PYMODULE')])
