#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt冲突修复工具
解决PyQt5和PyQt6同时存在导致的打包冲突问题
"""

import sys
import subprocess
from pathlib import Path

def check_qt_installations():
    """检查Qt安装情况"""
    print("🔍 检查Qt安装情况...")
    
    pyqt5_installed = False
    pyqt6_installed = False
    
    try:
        import PyQt5  # noqa: F401
        pyqt5_installed = True
        print("✓ 检测到PyQt5")
    except ImportError:
        print("❌ 未安装PyQt5")
    
    try:
        import PyQt6  # noqa: F401
        pyqt6_installed = True
        print("✓ 检测到PyQt6")
    except ImportError:
        print("❌ 未安装PyQt6")
    
    return pyqt5_installed, pyqt6_installed

def uninstall_pyqt6():
    """卸载PyQt6相关包"""
    print("\n🗑️ 卸载PyQt6相关包...")
    
    pyqt6_packages = [
        "PyQt6",
        "PyQt6-Qt6",
        "PyQt6-sip",
        "PyQt6-WebEngine",
        "PyQt6-WebEngine-Qt6"
    ]
    
    for package in pyqt6_packages:
        try:
            print(f"  卸载 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "uninstall", package, "-y"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"  ✓ {package} 卸载成功")
            else:
                print(f"  ⚠️ {package} 可能未安装")
        except Exception as e:
            print(f"  ❌ 卸载 {package} 失败: {e}")

def install_pyqt5():
    """安装PyQt5相关包"""
    print("\n📦 安装PyQt5相关包...")
    
    pyqt5_packages = [
        "PyQt5>=5.15.0",
        "PyQtWebEngine>=5.15.0"
    ]
    
    for package in pyqt5_packages:
        try:
            print(f"  安装 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, check=True)
            print(f"  ✓ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ {package} 安装失败: {e}")
            return False
    
    return True

def build_exe_with_pyqt5():
    """使用PyQt5构建EXE"""
    print("\n🔨 使用PyQt5构建EXE...")
    
    if not Path("main.py").exists():
        print("❌ 未找到main.py文件")
        return False
    
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name=快手采集工具",
        "--add-data=类目响应数据.md;.",
        "--add-data=data;data",
        "--hidden-import=PyQt5.QtCore",
        "--hidden-import=PyQt5.QtGui", 
        "--hidden-import=PyQt5.QtWidgets",
        "--hidden-import=PyQt5.QtWebEngineWidgets",
        "--hidden-import=PyQt5.QtWebEngineCore",
        "--hidden-import=requests",
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--exclude-module=PyQt6",
        "--exclude-module=PyQt6.QtCore",
        "--exclude-module=PyQt6.QtGui",
        "--exclude-module=PyQt6.QtWidgets",
        "--exclude-module=PyQt6.QtWebEngineWidgets",
        "main.py"
    ]
    
    # 添加其他数据文件
    optional_files = [
        "类目补充响应数据.md",
        "响应数据格式.md", 
        "商品成交量响应数据格式.md",
        "README.md"
    ]
    
    for file in optional_files:
        if Path(file).exists():
            cmd.insert(-1, f"--add-data={file};.")
    
    print("执行命令:")
    print(" ".join(cmd))
    print("\n这可能需要几分钟时间，请耐心等待...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功！")
            
            exe_file = Path("dist/快手采集工具.exe")
            if exe_file.exists():
                file_size = exe_file.stat().st_size / (1024 * 1024)
                print(f"✓ EXE文件: {exe_file}")
                print(f"✓ 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ EXE文件未生成")
                return False
        else:
            print("❌ 构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Qt冲突修复工具")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7+")
        input("按回车键退出...")
        return
    
    print(f"✓ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # 检查Qt安装情况
    pyqt5_installed, pyqt6_installed = check_qt_installations()
    
    if pyqt5_installed and pyqt6_installed:
        print("\n⚠️ 检测到同时安装了PyQt5和PyQt6")
        print("这会导致PyInstaller打包冲突")
        print("\n建议解决方案：")
        print("1. 卸载PyQt6，保留PyQt5（推荐）")
        print("2. 手动卸载其中一个版本")
        
        choice = input("\n是否自动卸载PyQt6并重新安装PyQt5？(y/n): ").lower().strip()
        
        if choice == 'y':
            # 卸载PyQt6
            uninstall_pyqt6()
            
            # 重新安装PyQt5
            if not install_pyqt5():
                print("❌ PyQt5安装失败")
                input("按回车键退出...")
                return
            
            print("\n✅ Qt冲突已解决！")
            
            # 尝试构建EXE
            choice2 = input("\n是否立即构建EXE？(y/n): ").lower().strip()
            if choice2 == 'y':
                if build_exe_with_pyqt5():
                    print("\n🎉 EXE构建成功！")
                    print("📁 文件位置: dist/快手采集工具.exe")
                else:
                    print("\n💥 EXE构建失败！")
        else:
            print("\n请手动解决Qt版本冲突后重试")
            
    elif pyqt5_installed:
        print("\n✅ 只安装了PyQt5，没有冲突")
        choice = input("是否立即构建EXE？(y/n): ").lower().strip()
        if choice == 'y':
            if build_exe_with_pyqt5():
                print("\n🎉 EXE构建成功！")
                print("📁 文件位置: dist/快手采集工具.exe")
            else:
                print("\n💥 EXE构建失败！")
                
    elif pyqt6_installed:
        print("\n⚠️ 只安装了PyQt6")
        print("建议卸载PyQt6并安装PyQt5以获得更好的兼容性")
        
        choice = input("是否自动切换到PyQt5？(y/n): ").lower().strip()
        if choice == 'y':
            uninstall_pyqt6()
            if install_pyqt5():
                print("\n✅ 已切换到PyQt5！")
                choice2 = input("是否立即构建EXE？(y/n): ").lower().strip()
                if choice2 == 'y':
                    if build_exe_with_pyqt5():
                        print("\n🎉 EXE构建成功！")
                        print("📁 文件位置: dist/快手采集工具.exe")
                    else:
                        print("\n💥 EXE构建失败！")
            else:
                print("❌ PyQt5安装失败")
        
    else:
        print("\n❌ 未安装PyQt5或PyQt6")
        choice = input("是否安装PyQt5？(y/n): ").lower().strip()
        if choice == 'y':
            if install_pyqt5():
                print("\n✅ PyQt5安装成功！")
                choice2 = input("是否立即构建EXE？(y/n): ").lower().strip()
                if choice2 == 'y':
                    if build_exe_with_pyqt5():
                        print("\n🎉 EXE构建成功！")
                        print("📁 文件位置: dist/快手采集工具.exe")
                    else:
                        print("\n💥 EXE构建失败！")
            else:
                print("❌ PyQt5安装失败")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
